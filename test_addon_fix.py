# ملف اختبار للتأكد من أن إصلاحات البحث عن الإضافة تعمل بشكل صحيح
import bpy

def test_addon_preferences():
    """
    اختبار دالة البحث عن إعدادات الإضافة
    """
    context = bpy.context
    
    # اختبار الدالة المساعدة من الملف الرئيسي
    try:
        from . import get_addon_preferences
        
        # اختبار البحث عن إعدادات مختلفة
        test_preferences = [
            'Camera_Manager',
            'Light_Manager', 
            'KH_Material',
            'KH_Asset',
            'KH_TopView',
            'KH_Sketchup',
            'KH_Tutoril',
            'HIDDEN_LIST'
        ]
        
        print("=== اختبار دالة البحث عن إعدادات الإضافة ===")
        
        for pref_name in test_preferences:
            try:
                result = get_addon_preferences(context, pref_name)
                print(f"✅ {pref_name}: {result}")
            except Exception as e:
                print(f"❌ {pref_name}: خطأ - {e}")
        
        print("=== انتهاء الاختبار ===")
        
    except ImportError as e:
        print(f"❌ فشل في استيراد الدالة المساعدة: {e}")
        
        # اختبار الطريقة القديمة (يجب أن تفشل)
        try:
            addon = context.preferences.addons['KH-Tools']
            print("⚠️ الطريقة القديمة ما زالت تعمل - قد تحتاج لإعادة تشغيل Blender")
        except KeyError:
            print("✅ الطريقة القديمة فشلت كما هو متوقع")

if __name__ == "__main__":
    test_addon_preferences()
