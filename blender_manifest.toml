# ملف البيان للإضافة KH-Tools
# Manifest file for KH-Tools extension

schema_version = "1.0.0"

# معرف فريد للإضافة
# Unique identifier for the extension
id = "kh_tools"

# إصدار الإضافة
# Extension version
version = "1.94.0"

# اسم الإضافة
# Extension name
name = "KH-Tools"

# وصف مختصر للإضافة
# Short description of the extension
tagline = "Professional architectural visualization tools for Blender"

# مطور الإضافة
# Extension maintainer
maintainer = "Khaled Alnwesary"

# نوع الإضافة
# Extension type
type = "add-on"

# الحد الأدنى لإصدار بلندر المدعوم
# Minimum supported Blender version
blender_version_min = "4.2.0"

# الترخيص
# License
license = [
  "SPDX:GPL-3.0-or-later",
]

# حقوق الطبع والنشر
# Copyright
copyright = [
  "2024 Khaled Alnwesary",
]

# العلامات التصنيفية
# Tags for categorization
tags = [
  "3D View",
  "Import-Export", 
  "Lighting",
  "Material",
  "Render",
  "Scene",
  "Animation"
  "Camera"
]

# الأذونات المطلوبة
# Required permissions
[permissions]
files = "Import/export assets and manage project files"
network = "Download assets and check for updates"

# إعدادات البناء
# Build settings
[build]
paths_exclude_pattern = [
  "__pycache__/",
  "/.git/",
  "*.zip",
  "*.pyc",
  ".DS_Store",
  "Thumbs.db",
  "*.tmp",
  "*.bak"
]
